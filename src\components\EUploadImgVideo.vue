 <template>
   <!-- 支持同时上传图片和视频，或者只上传图片、只上传视频，可以预览、删除 -->
   <div class="any-upload-img-video">
     <el-upload
       :action="uploadApi"
       list-type="picture-card"
       :limit="limit"
       :disabled="disabled"
       :multiple="multiple"
       :accept="accept"
       :file-list="fileList"
       :on-exceed="handleExceed"
       :on-change="(file, fileList) => handleChange(file, fileList)"
       :on-success="(response, file, fileList) => handleSuccess(response, file, fileList)"
       :before-upload="file => beforeUpload(file)"
     >
       <template #default>
         <i class="el-icon-plus iconc scolor"></i>
         <p class="scolor" v-if="props.onlyImage">上传图片</p>
         <p class="scolor" v-else-if="props.onlyVideo">上传视频</p>
         <p class="scolor" v-else-if="props.onlyDocument">上传文档</p>
         <p class="scolor" v-else>上传文件</p>
       </template>
       <template #file="{ file }">
         <div class="custom-file-list">
           <img class="el-icon-close" @click="handleRemove(file)" src="@/assets/fdelete.png" alt="">
           <!-- 图片和视频显示缩略图 -->
           <img
             v-if="isImageOrVideo(file)"
             class="el-upload-list__item-thumbnail"
             :src="file.url"
             alt=""
           />
           <!-- 文档显示文件图标 -->
           <div v-else class="document-preview">
             <i class="el-icon-document document-icon"></i>
             <p class="document-name">{{ getFileName(file) }}</p>
           </div>
           <el-progress
             type="circle"
             v-if="showProgress && file.url == uploadUrl"
             :percentage="Number(file.percentage)"
             :width="64">
           </el-progress>
         </div>
       </template>
     </el-upload>
     <el-row class="limit-tip">
         <slot></slot>
     </el-row>
   </div>
 </template>
 
 <script setup>
 import { ElMessage } from 'element-plus'

 const uidGenerator = () => {
   return '-' + parseInt(Math.random() * 10000 + 1, 10)
 }

 const props = defineProps({
   // 是否支持多选
   multiple: {
     type: Boolean,
     default: () => false
   },
   // 绑定的数据
   modelValue: {
     type: [Array],
     required: false
   },
   // 禁用上传
   disabled: {
     type: Boolean,
     default: false,
     required: false
   },
   // 文件类型
   accept: {
     type: String,
     default: 'video/mp4,video/ogg,video/webm,image/png,image/jpg,image/gif,image/jpeg,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation'
   },
   // 上传时附带的额外参数
   acceptData: {
     type: Object,
     default: () => {
       return {
         accept: '.mp4,.ogg,.webm,.png,.jpg,.gif,.jpeg,.pdf,.doc,.docx,.ppt,.pptx', // 同时支持视频、图片和文档时
         maxsize: 1024 * 1024 * 500, // 500M
         maxsize_text: '500M',
         videoTypeList: ['video/mp4', 'video/ogg', 'video/webm'],
         imageTypeList: ['image/png', 'image/jpg', 'image/jpeg', 'image/gif'],
         documentTypeList: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation']
       }
     }
   },
   // 最大数量
   limit: {
     type: Number,
     default: 10
   },
   // 仅上传图片
   onlyImage: {
     type: Boolean,
     default: () => false
   },
   // 仅上传视频
   onlyVideo: {
     type: Boolean,
     default: () => false
   },
   // 仅上传文档
   onlyDocument: {
     type: Boolean,
     default: () => false
   }
 })

 const emit = defineEmits(['update:modelValue'])

 const fileList = ref([]) // 上传的文件数据
 const showProgress = ref(false)
 const uploadPercentage = ref(0)
 const uploadApi = ref(import.meta.env.VITE_GLOB_BASE_URL + '/aliyun/oss/uploadFiles')
 const uploadUrl = ref("")

 // 图片/视频回显
 const initFileList = (array) => {
   if (!array || array.length === 0) {
     fileList.value = []
     return
   }
   const newFileList = []
   const arr = array || []
   for (let i = 0; i < arr.length; i++) {
     newFileList.push({
      ...arr[i],
       uid: arr[i].uid ? arr[i].uid : uidGenerator(),
     })
   }
   fileList.value = newFileList
 }

 watch(() => props.modelValue, (val) => {
   initFileList(val)
 }, { immediate: true })

 // 其他附件上传大小限制处理
 const beforeUpload = (file) => {
   const { videoTypeList, imageTypeList, documentTypeList } = props.acceptData
   let isVideo = true
   if (videoTypeList) {
     isVideo = videoTypeList.indexOf(file.type) !== -1
   }

   let isImg = true
   if (imageTypeList) {
     isImg = imageTypeList.indexOf(file.type) !== -1
   }

   let isDocument = true
   if (documentTypeList) {
     isDocument = documentTypeList.indexOf(file.type) !== -1
   }

   // 只支持视频
   if (props.onlyVideo) {
     if (!isVideo) {
      ElMessage.error(`只能上传${props.acceptData.accept}的文件!`)
       return false
     }
   }

   // 只支持图片
   if (props.onlyImage) {
     if (!isImg) {
      ElMessage.error(`只能上传${props.acceptData.accept}格式的文件!`)
       return false
     }
   }

   // 只支持文档
   if (props.onlyDocument) {
     if (!isDocument) {
      ElMessage.error(`只能上传${props.acceptData.accept}格式的文件!`)
       return false
     }
   }

   // 默认：视频、图片和文档都支持
   if (!props.onlyVideo && !props.onlyImage && !props.onlyDocument) {
     if (!isVideo && !isImg && !isDocument) {
      ElMessage.error(`只能上传${props.acceptData.accept}格式的文件!`)
       return false
     }
   }

   // 文件大小限制
   const isLessThanMaxSize = file.size < props.acceptData.maxsize
   if (!isLessThanMaxSize) {
    ElMessage.error(`上传文件大小不能超过${props.acceptData.maxsize_text}!`)
     return false
   }
   return true
 }
 // 文件个数限制
 const handleExceed = () => {
  ElMessage.error(`当前限制最多上传 ${props.limit} 个文件!`)
 }

 const handleChange = (file, fileListParam) => {
   uploadPercentage.value = 0
   if (file.status === 'ready') {
     showProgress.value = true
     uploadUrl.value = file.url
     const interval = setInterval(() => {
       if (uploadPercentage.value >= 95) {
         clearInterval(interval)
       }
       uploadPercentage.value += 1
     }, 20)
   }
   if (file.status === 'success') {
     uploadUrl.value = file.url
     uploadPercentage.value = 100
     showProgress.value = false
   }
 }

 // 上传图片成功
 const handleSuccess = (response, file, fileListParam) => {
   showProgress.value = false
   if (response.status === 0) {
      ElMessage({
        message: '上传成功',
        type: 'success'
      })
     const attachment = fileListParam.map(item => {
       return {
         name: item.response ? item.response.data.fileName : item.name,
         url: item.response ? item.response.data.url : item.url,
         uid: item.uid,
         type: item.raw ? item.raw.type : item.type,
         size: item.response ? item.response.data.size : item.size
       }
     })
     fileList.value = attachment
     emit("update:modelValue", fileList.value)
   } else {
     ElMessage(response.msg)
   }
 }

 // 删除
 const handleRemove = (file) => {
   let index
   fileList.value.find((item, idx) => {
     if (item.uid === file.uid) {
       index = idx
       return
     }
   })
   if (typeof index !== 'undefined') {
     fileList.value.splice(index, 1)
   }
   emit("update:modelValue", fileList.value)
 }

 // 判断是否为图片或视频文件
 const isImageOrVideo = (file) => {
   const { videoTypeList, imageTypeList } = props.acceptData
   const fileType = file.raw ? file.raw.type : file.type
   return videoTypeList.includes(fileType) || imageTypeList.includes(fileType)
 }

 // 获取文件名
 const getFileName = (file) => {
   return file.name || file.response?.data?.fileName || '未知文件'
 }
 </script>
 
 <style lang="scss" scoped>
  .scolor{
    color: #386CFC;
  }
  :deep(){
    .el-upload-list--picture-card .el-upload-list__item-thumbnail {
      width: 100%;
      height: 100%;
    }
    .custom-file-list .el-icon-close {
        position: absolute;
        right: 0;
        top: 0;
        z-index: 1000;
        cursor: pointer;
        display: block !important;
      }
    
      .el-upload-list--picture-card .el-upload-list__item {
      width: 180px;
      height: 135px;
      border-radius: 8px 8px 8px 8px;
      border: none !important;
    }
      .el-upload--picture-card {
        width: 180px;
        height: 135px;
        background: #F0F1F5;
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #F5F6FA;
        line-height: 20px;
    }
      .el-upload-list--picture-card .el-progress {
      width: 75px !important;
    }
      .el-progress-circle {
      width: 75px !important;
      height: 75px !important;
    }
  }
 .limit-tip {
   color: #999999;
   display: inline-block;
   line-height: 20px;
   margin-top: 5px;
   font-size: 14px;
 }
 .iconc{
  margin-top: 41px;
 }
 
 .custom-file-list {
   position: relative;
   font-size: 16px;
   height: 100%;
 }

 .document-preview {
   display: flex;
   flex-direction: column;
   align-items: center;
   justify-content: center;
   height: 100%;
   padding: 10px;
   text-align: center;
 }

 .document-icon {
   font-size: 40px;
   color: #409EFF;
   margin-bottom: 8px;
 }

 .document-name {
   font-size: 12px;
   color: #666;
   margin: 0;
   word-break: break-all;
   overflow: hidden;
   display: -webkit-box;
   -webkit-line-clamp: 2;
   -webkit-box-orient: vertical;
 }


 </style>